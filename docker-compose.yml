version: '3.8'

services:
  # Backend Service - FastAPI Application
  backend:
    build:
      context: ./backend
      dockerfile: Dockerfile
    container_name: ai-coding-agent-backend
    ports:
      - "8000:8000"
    volumes:
      # Persistent data volumes
      - user-projects:/app/user-projects
      - backend-logs:/app/logs
      # vector-db-data removed - now using pgvector via Supabase
      # Configuration
      - ./backend/config:/app/config:ro
    environment:
      - ENVIRONMENT=production
      - HOST=0.0.0.0
      - PORT=8000
      - PYTHONPATH=/app
      # Security keys from .env
      - SECRET_KEY=${SECRET_KEY}
      - CONFIG_ENCRYPTION_KEY=${CONFIG_ENCRYPTION_KEY}
      # Database configuration
      - DATABASE_URL=postgresql://agent:${DB_PASSWORD:-password}@postgres:5432/ai_coding_agent
      # AI Service configuration
      - OLLAMA_HOST=http://ollama:11434
      # VECTOR_DB_HOST removed - now using pgvector via Supabase
      # Redis configuration
      - REDIS_URL=redis://redis:6379
    env_file:
      - .env
    depends_on:
      postgres:
        condition: service_healthy
      redis:
        condition: service_started
    networks:
      - ai-coding-agent-network
    restart: unless-stopped
    healthcheck:
      test: ["CMD", "curl", "-f", "http://localhost:8000/api/v1/health"]
      interval: 30s
      timeout: 10s
      retries: 3
      start_period: 40s

  # Frontend Service - React Application
  frontend:
    build:
      context: ./frontend
      dockerfile: Dockerfile
    container_name: ai-coding-agent-frontend
    ports:
      - "3000:80"
    environment:
      - NODE_ENV=production
      - REACT_APP_API_URL=http://localhost:8000
    depends_on:
      backend:
        condition: service_healthy
    networks:
      - ai-coding-agent-network
    restart: unless-stopped
    healthcheck:
      test: ["CMD", "wget", "--no-verbose", "--tries=1", "--spider", "http://127.0.0.1:80/"]
      interval: 30s
      timeout: 10s
      retries: 3
      start_period: 40s

  # Vector Database Service removed - now using pgvector via Supabase

  # PostgreSQL Database
  postgres:
    image: postgres:15-alpine
    container_name: ai-coding-agent-postgres
    environment:
      - POSTGRES_DB=ai_coding_agent
      - POSTGRES_USER=agent
      - POSTGRES_PASSWORD=${DB_PASSWORD:-password}
      - POSTGRES_INITDB_ARGS=--encoding=UTF-8 --lc-collate=C --lc-ctype=C
    volumes:
      - postgres-data:/var/lib/postgresql/data
      - ./database/init:/docker-entrypoint-initdb.d:ro
    ports:
      - "5432:5432"
    networks:
      - ai-coding-agent-network
    restart: unless-stopped
    healthcheck:
      test: ["CMD-SHELL", "pg_isready -U agent -d ai_coding_agent"]
      interval: 10s
      timeout: 5s
      retries: 5

  # Ollama AI Service (Optional - can be external)
  # Temporarily disabled - using local Ollama installation
  # ollama:
  #   image: ollama/ollama:latest
  #   container_name: ai-coding-agent-ollama
  #   ports:
  #     - "11434:11434"
  #   volumes:
  #     - ollama-data:/root/.ollama
  #   environment:
  #     - OLLAMA_HOST=0.0.0.0
  #   networks:
  #     - ai-coding-agent-network
  #   restart: unless-stopped
  #   deploy:
  #     resources:
  #       reservations:
  #         devices:
  #           - driver: nvidia
  #             count: 1
  #             capabilities: [gpu]
  #   healthcheck:
  #     test: ["CMD", "curl", "-f", "http://localhost:11434/api/tags"]
  #     interval: 30s
  #     timeout: 10s
  #     retries: 3
  #     start_period: 60s

  # Redis Cache Service
  redis:
    image: redis:7-alpine
    container_name: ai-coding-agent-redis
    ports:
      - "6379:6379"
    volumes:
      - redis-data:/data
    networks:
      - ai-coding-agent-network
    restart: unless-stopped
    command: redis-server --appendonly yes
    healthcheck:
      test: ["CMD", "redis-cli", "ping"]
      interval: 30s
      timeout: 10s
      retries: 3
      start_period: 10s

# Named Volumes for Data Persistence
volumes:
  user-projects:
    driver: local
    driver_opts:
      type: none
      o: bind
      device: ./user-projects
  postgres-data:
    driver: local
  redis-data:
    driver: local
  backend-logs:
    driver: local
  ollama-data:
    driver: local

# Network Configuration
networks:
  ai-coding-agent-network:
    driver: bridge
    ipam:
      config:
        - subnet: **********/16
