version: '3.8'

# Development override for docker-compose.yml
# Use with: docker-compose -f docker-compose.yml -f docker-compose.dev.yml up

services:
  backend:
    build:
      target: development
    volumes:
      # Enhanced volume mounts for development
      - ./backend/src:/app/src
      - ./backend/tests:/app/tests
      - ./backend/config:/app/config
      - ./backend/requirements.txt:/app/requirements.txt
      - ./backend/requirements-dev.txt:/app/requirements-dev.txt
      # Persistent data (same as production)
      - user-projects:/app/user-projects
      - backend-logs:/app/logs
      # vector-db-data removed - now using pgvector via Supabase
    environment:
      - ENVIRONMENT=development
      - DEBUG=true
      - RELOAD=true
      - LOG_LEVEL=debug
    command: ["uvicorn", "ai_coding_agent.main:app", "--host", "0.0.0.0", "--port", "8000", "--reload", "--log-level", "debug"]
    ports:
      - "8000:8000"
      - "5678:5678"  # Debug port for VS Code

  frontend:
    build:
      target: development
    volumes:
      # Mount entire source for hot reloading
      - ./frontend/src:/app/src
      - ./frontend/public:/app/public
      - ./frontend/package.json:/app/package.json
      - ./frontend/package-lock.json:/app/package-lock.json
      - ./frontend/tsconfig.json:/app/tsconfig.json
      - ./frontend/tailwind.config.js:/app/tailwind.config.js
      - ./frontend/postcss.config.js:/app/postcss.config.js
    environment:
      - NODE_ENV=development
      - REACT_APP_API_URL=http://localhost:8000
      - CHOKIDAR_USEPOLLING=true
      - FAST_REFRESH=true
    command: ["npm", "start"]
    ports:
      - "3000:3000"  # Development server port

  # vector-db service removed - now using pgvector via Supabase

  postgres:
    environment:
      - POSTGRES_LOG_STATEMENT=all
      - POSTGRES_LOG_MIN_DURATION_STATEMENT=0
    volumes:
      # Add development database scripts
      - ./database/dev-init:/docker-entrypoint-initdb.d:ro
      - postgres-dev-data:/var/lib/postgresql/data
    ports:
      - "5432:5432"

  # Development Tools
  pgadmin:
    image: dpage/pgadmin4:latest
    container_name: ai-coding-agent-pgadmin
    environment:
      - PGADMIN_DEFAULT_EMAIL=<EMAIL>
      - PGADMIN_DEFAULT_PASSWORD=admin
      - PGADMIN_CONFIG_SERVER_MODE=False
    ports:
      - "8080:80"
    volumes:
      - pgadmin-data:/var/lib/pgadmin
    networks:
      - ai-coding-agent-network
    depends_on:
      - postgres

  # Redis for development caching (optional)
  redis:
    image: redis:7-alpine
    container_name: ai-coding-agent-redis
    ports:
      - "6379:6379"
    volumes:
      - redis-data:/data
    networks:
      - ai-coding-agent-network
    command: redis-server --appendonly yes

volumes:
  postgres-dev-data:
    driver: local
  pgadmin-data:
    driver: local
  redis-data:
    driver: local
