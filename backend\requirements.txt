# AI Coding Agent - Production Dependencies
# Core web framework
fastapi>=0.104.0
uvicorn[standard]>=0.24.0

# Configuration and settings
pydantic>=2.4.0
pydantic-settings>=2.0.0
email-validator>=2.0.0
python-dotenv>=1.0.0
pyyaml>=6.0.0

# Database
sqlalchemy>=2.0.0
asyncpg>=0.29.0
psycopg2-binary==2.9.10
alembic>=1.13.0
supabase>=2.0.0

# Vector Database for LTKB/STPM (pgvector via Supabase)
# chromadb>=0.4.0  # REMOVED: Replaced with pgvector

# Redis for caching and real-time features
redis>=5.0.0
hiredis>=2.2.0

# Authentication and security
python-jose[cryptography]>=3.3.0
passlib[bcrypt]>=1.7.4
python-multipart>=0.0.6

# File handling
aiofiles>=23.2.0

# AI and ML
langchain>=0.1.0
langchain-community>=0.0.10
langchain-ollama>=0.1.0

# Ollama client
ollama>=0.1.0

# HTTP client
httpx>=0.25.0
aiohttp>=3.9.0

# Logging
structlog>=23.2.0
