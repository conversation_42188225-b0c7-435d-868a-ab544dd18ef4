# AI Coding Agent Backend Container
# Multi-stage build for production optimization

# Development stage
FROM python:3.11-slim as development

# Set environment variables
ENV PYTHONDONTWRITEBYTECODE=1 \
    PYTHONUNBUFFERED=1 \
    PYTHONPATH=/app \
    PIP_NO_CACHE_DIR=1 \
    PIP_DISABLE_PIP_VERSION_CHECK=1

# Install system dependencies
RUN apt-get update && apt-get install -y \
    curl \
    gcc \
    g++ \
    git \
    && rm -rf /var/lib/apt/lists/*

# Set work directory
WORKDIR /app

# Copy requirements first for better caching
COPY requirements.txt .
COPY requirements-dev.txt .

# Install Python dependencies
RUN pip install --no-cache-dir -r requirements.txt

# Copy application code with correct paths
COPY src/ai_coding_agent/ ./ai_coding_agent/
COPY config/ ./config/
COPY scripts/ ./scripts/

# Copy tests for migration testing
COPY tests/ ./tests/

# Create necessary directories (vector_db removed - now using pgvector via Supabase)
RUN mkdir -p logs uploads user-projects

# Expose port
EXPOSE 8000

# Development command with hot reload
CMD ["uvicorn", "ai_coding_agent.main:app", "--host", "0.0.0.0", "--port", "8000", "--reload"]

# Production stage
FROM python:3.11-slim as production

# Set environment variables
ENV PYTHONDONTWRITEBYTECODE=1 \
    PYTHONUNBUFFERED=1 \
    PYTHONPATH=/app \
    PIP_NO_CACHE_DIR=1 \
    PIP_DISABLE_PIP_VERSION_CHECK=1

# Create non-root user for security
RUN groupadd -r appuser && useradd -r -g appuser appuser

# Install system dependencies
RUN apt-get update && apt-get install -y \
    curl \
    && rm -rf /var/lib/apt/lists/*

# Set work directory
WORKDIR /app

# Copy requirements first for better caching
COPY requirements.txt .
COPY requirements-dev.txt .

# Install Python dependencies
RUN pip install --no-cache-dir -r requirements.txt

# Copy application code with correct paths
COPY src/ai_coding_agent/ ./ai_coding_agent/
COPY config/ ./config/
COPY scripts/ ./scripts/

# Copy tests for migration testing
COPY tests/ ./tests/

# Create necessary directories and set permissions (vector_db removed)
RUN mkdir -p logs uploads user-projects && \
    chown -R appuser:appuser /app

# Switch to non-root user
USER appuser

# Health check
HEALTHCHECK --interval=30s --timeout=10s --start-period=40s --retries=3 \
    CMD curl -f http://localhost:8000/api/v1/health || exit 1

# Expose port
EXPOSE 8000

# Production command without reload
CMD ["uvicorn", "ai_coding_agent.main:app", "--host", "0.0.0.0", "--port", "8000"]
