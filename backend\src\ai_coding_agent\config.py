"""
Configuration management for AI Coding Agent.

This module provides centralized configuration management using Pydantic Settings
with environment variable support and validation.
"""

from functools import lru_cache
from typing import Literal, Any, Optional

from pydantic import Field, field_validator
from pydantic_settings import BaseSettings


class DatabaseSettings(BaseSettings):
    """Database configuration settings."""

    host: str = Field(default="localhost", description="Database host")
    port: int = Field(default=5432, description="Database port")
    name: str = Field(default="ai_coding_agent", description="Database name")
    user: str = Field(default="postgres", description="Database user")
    password: str = Field(
        default="", description="Database password"
    )

    model_config = {"env_prefix": "DB_", "env_file": ".env", "env_file_encoding": "utf-8", "extra": "ignore"}

    @property
    def url(self) -> str:
        """Get the database URL."""
        return f"postgresql://{self.user}:{self.password}@{self.host}:{self.port}/{self.name}"


class SupabaseSettings(BaseSettings):
    """Supabase configuration settings."""

    url: str = Field(default="", description="Supabase project URL")
    anon_key: str = Field(default="", description="Supabase anonymous key")
    service_role_key: str = Field(default="", description="Supabase service role key")

    model_config = {"env_prefix": "SUPABASE_", "env_file": ".env", "env_file_encoding": "utf-8", "extra": "ignore"}

    @property
    def database_url(self) -> str:
        """Get the Supabase PostgreSQL connection URL."""
        if not self.url:
            return ""
        # Extract project reference from URL
        project_ref = self.url.split("//")[1].split(".")[0]
        return f"postgresql://postgres:[YOUR-PASSWORD]@db.{project_ref}.supabase.co:5432/postgres"


class RedisSettings(BaseSettings):
    """Redis configuration settings."""

    url: str = Field(
        default="redis://localhost:6379", description="Redis connection URL"
    )
    password: Optional[str] = Field(
        default=None, description="Redis password (if required)"
    )
    db: int = Field(
        default=0, description="Redis database number"
    )
    max_connections: int = Field(
        default=20, description="Maximum Redis connections in pool"
    )
    socket_timeout: int = Field(
        default=5, description="Redis socket timeout in seconds"
    )
    socket_connect_timeout: int = Field(
        default=5, description="Redis connection timeout in seconds"
    )

    model_config = {"env_prefix": "REDIS_", "env_file": ".env", "env_file_encoding": "utf-8", "extra": "ignore"}


class DatabaseSettings(BaseSettings):
    """Database configuration settings - Simplified to Supabase + Redis."""

    mode: Literal["supabase"] = Field(
        default="supabase", description="Database mode - now only Supabase with pgvector"
    )
    # SQLite removed - replaced with Redis cache
    # sqlite_url: str = Field(
    #     default="sqlite:///./roadmap_test.db", description="SQLite database URL"
    # )

    # All data now stored in Supabase with pgvector for embeddings
    supabase_tables: str = Field(
        default="best_practices,tech_stack_metadata,security_policies,frameworks,libraries,embeddings,projects,roadmaps,phases,steps,tasks",
        description="All tables now stored in Supabase"
    )

    model_config = {"env_prefix": "", "env_file": ".env", "env_file_encoding": "utf-8", "extra": "ignore"}

    @property
    def local_table_list(self) -> list[str]:
        """Get list of local tables."""
        return [table.strip() for table in self.local_tables.split(",")]

    @property
    def supabase_table_list(self) -> list[str]:
        """Get list of Supabase tables."""
        return [table.strip() for table in self.supabase_tables.split(",")]


class AISettings(BaseSettings):
    """AI service configuration settings with specialized agent models."""

    ollama_host: str = Field(
        default="http://localhost:11434", description="Ollama host URL"
    )

    # Default fallback model
    default_model: str = Field(
        default="llama3.2:3b", description="Default fallback model"
    )

    # Specialized Agent Models - Optimized for specific roles
    architect_agent_model: str = Field(
        default="llama3.2:3b",
        description="Architect Agent - Project planning and orchestration - Great for planning & orchestration",
    )
    frontend_agent_model: str = Field(
        default="starcoder2:3b",
        description="Frontend Agent - UI/UX code generation - Perfect for UI/UX & React"
    )
    backend_agent_model: str = Field(
        default="deepseek-coder:6.7b-instruct",
        description="Backend Agent - API and server-side logic - Better for complex logic"
    )
    shell_agent_model: str = Field(
        default="qwen2.5:3b",
        description="Shell Agent - Command execution and system tasks - Good for system commands",
    )
    debug_agent_model: str = Field(
        default="deepseek-coder:6.7b-instruct",
        description="Debug Agent - Issue detection and fixes - Excellent choice for debugging",
    )
    test_agent_model: str = Field(
        default="qwen2.5:3b",
        description="Test Agent - Unit tests and test strategy - Better code understanding"
    )

    # Code-specific models for specialized tasks
    code_completion_model: str = Field(
        default="starcoder2:3b",
        description="Fast code completion and autocomplete - Fast and efficient"
    )
    code_generation_model: str = Field(
        default="deepseek-coder:6.7b-instruct",
        description="General code generation - Superior structured generation"
    )
    code_review_model: str = Field(
        default="deepseek-coder:6.7b-instruct",
        description="Advanced code analysis and review - Perfect for analysis"
    )

    # Chat and reasoning models
    chat_model: str = Field(
        default="llama3.2:3b",
        description="General chat and quick responses - Great for communication"
    )
    documentation_model: str = Field(
        default="llama3.2:3b",
        description="Documentation generation and technical writing - Good for docs"
    )

    # Performance settings
    max_tokens: int = Field(default=4096, description="Maximum tokens per request")
    temperature: float = Field(default=0.7, description="AI temperature setting")

    # Health routing settings
    unhealthy_model_timeout: int = Field(default=300, description="Timeout for unhealthy models in seconds")
    auto_fallback_on_failure: bool = Field(default=True, description="Enable automatic fallback on model failure")
    health_score_threshold: float = Field(default=0.8, description="Minimum health score for model selection")
    recovery_check_interval: int = Field(default=60, description="Interval to check for model recovery in seconds")
    max_consecutive_failures: int = Field(default=3, description="Max failures before marking model unhealthy")

    # Task-specific timeouts
    code_completion_timeout: int = Field(default=30, description="Timeout for code completion tasks")
    code_generation_timeout: int = Field(default=90, description="Timeout for code generation tasks")
    complex_generation_timeout: int = Field(default=180, description="Timeout for complex generation tasks")
    debugging_timeout: int = Field(default=120, description="Timeout for debugging tasks")
    planning_timeout: int = Field(default=150, description="Timeout for planning tasks")
    system_commands_timeout: int = Field(default=60, description="Timeout for system command tasks")
    unit_testing_timeout: int = Field(default=90, description="Timeout for unit testing tasks")
    documentation_timeout: int = Field(default=120, description="Timeout for documentation tasks")

    model_config = {"env_prefix": "AI_", "env_file": ".env", "env_file_encoding": "utf-8", "extra": "ignore"}

    @field_validator("temperature")
    @classmethod
    def validate_temperature(cls, v: float) -> float:
        """Validate temperature is between 0 and 2."""
        if not 0 <= v <= 2:
            raise ValueError("Temperature must be between 0 and 2")
        return v

    @field_validator("health_score_threshold")
    @classmethod
    def validate_health_score(cls, v: float) -> float:
        """Validate health score threshold is between 0 and 1."""
        if not 0 <= v <= 1:
            raise ValueError("Health score threshold must be between 0 and 1")
        return v


class SecuritySettings(BaseSettings):
    """Security configuration settings."""

    secret_key: str = Field(default="", description="Secret key for JWT tokens")
    algorithm: str = Field(default="HS256", description="JWT algorithm")
    access_token_expire_minutes: int = Field(
        default=30, description="Access token expiry"
    )
    refresh_token_expire_days: int = Field(
        default=7, description="Refresh token expiry"
    )

    model_config = {"env_prefix": "SECURITY_", "env_file": ".env", "env_file_encoding": "utf-8", "extra": "ignore"}

    @field_validator("secret_key")
    @classmethod
    def validate_secret_key(cls, v: str) -> str:
        """Ensure secret key is provided and sufficiently long."""
        if not v:
            raise ValueError("SECRET_KEY must be provided")
        if len(v) < 32:
            raise ValueError("SECRET_KEY must be at least 32 characters long")
        return v


class AppSettings(BaseSettings):
    """Main application configuration settings."""

    app_name: str = Field(default="AI Coding Agent", description="Application name")
    version: str = Field(default="0.1.0", description="Application version")
    environment: Literal["development", "staging", "production", "testing"] = Field(
        default="development", description="Environment"
    )
    debug: bool = Field(default=False, description="Debug mode")
    host: str = Field(default="localhost", description="Host address")
    port: int = Field(default=8000, description="Port number")
    cors_origins: Any = Field(
        default=["http://localhost:3000", "http://localhost:8080"],
        description="CORS origins",
    )

    model_config = {"env_file": ".env", "env_file_encoding": "utf-8", "extra": "ignore"}

    @field_validator("cors_origins", mode="before")
    @classmethod
    def parse_cors_origins(cls, v):
        """Allow comma-separated strings for CORS origins."""
        if isinstance(v, str):
            return [origin.strip() for origin in v.split(",") if origin.strip()]
        return v

    @property
    def database(self) -> DatabaseSettings:
        """Get database settings."""
        return DatabaseSettings()

    @property
    def supabase(self) -> SupabaseSettings:
        """Get Supabase settings."""
        return SupabaseSettings()

    @property
    def redis(self) -> RedisSettings:
        """Get Redis settings."""
        return RedisSettings()

    # Removed hybrid_db - now using simplified Supabase + Redis architecture

    @property
    def ai(self) -> AISettings:
        """Get AI settings."""
        return AISettings()

    @property
    def security(self) -> SecuritySettings:
        """Get security settings."""
        return SecuritySettings()


@lru_cache()
def get_settings() -> AppSettings:
    """
    Get application settings with caching.

    This function uses LRU cache to ensure settings are loaded only once
    and reused across the application.
    """
    return AppSettings()


# Export commonly used settings
settings = get_settings()
